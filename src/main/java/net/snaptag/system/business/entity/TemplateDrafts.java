package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.common.BaseEntity;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/27 16:38
 * @description：编辑模板，由用户的打印记录中产生
 * @modified By：
 * @version: $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_template_drafts")
public class TemplateDrafts extends BaseEntity {

    public static final String TYPE_FIELD       = "type";
    public static final String LOCALE_CODE_FIELD       = "localeCode";
    public static final String SORT_NUM_FIELD       = "sortNum";
    public static final String IS_HOT = "isHot";
    public static final String RECOMMEND_COLUMN = "recommend";
    public static final String NAME_COLUMN = "name";
    public static final String CONTENT_COLUMN = "content";

    @TableField("name")
    private String name;            // 名称
    @TableField("content")
    private String content;         // 描述
    @TableField("recommend")
    private String recommend;       // 推荐描述

    /**
     * 图的具体信息 - JSON格式字符串
     * 参考打印记录里面的数据结构
     */
    @TableField(value = "drafts_dto")
    private String draftsDto;

    @TableField("pic")
    private String pic;             // 缩略图地址

    @TableField("type")
    private String type;            // 类型：居家收纳：living；厨房收纳：kitchen；办公收纳：office

    @TableField("is_hot")
    private int isHot;              // 是否热门。0：非热门； 1：热门

    @TableField("sort_num")
    private int sortNum;            // 排序字段
    @TableField("locale_code")
    private String localeCode;      // 语言国际

    @TableField("printer_type")
    private String printerType;     // 符合打印机的类型
    @TableField("paper_type")
    private String paperType;     // 符合打印机的类型
    @TableField("paper_size")
    private String paperSize;           // 纸张尺寸开

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getDraftsDto() {
        return draftsDto;
    }

    public void setDraftsDto(String draftsDto) {
        this.draftsDto = draftsDto;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getIsHot() {
        return isHot;
    }

    public void setIsHot(int isHot) {
        this.isHot = isHot;
    }

    public int getSortNum() {
        return sortNum;
    }

    public void setSortNum(int sortNum) {
        this.sortNum = sortNum;
    }

    public String getRecommend() {
        return recommend;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    public void setLocaleCode(String localeCode) {
        this.localeCode = localeCode;
    }

    public void setRecommend(String recommend) {
        this.recommend = recommend;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getPaperType() {
        return paperType;
    }

    public void setPaperType(String paperType) {
        this.paperType = paperType;
    }

    public String getPaperSize() {
        return paperSize;
    }

    public void setPaperSize(String paperSize) {
        this.paperSize = paperSize;
    }
}
