package net.snaptag.system.business.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.snaptag.system.business.vo.ColumnVo;
import net.snaptag.system.common.BaseEntity;

import java.util.Map;

/**
 * 消息栏目表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("v1_msg_column")
public class MsgColumn extends BaseEntity {
    private static final long serialVersionUID = 1L;
    public static final String COLL = "v1_msg_column";
    public static final String USERID_FIELD = "userId";

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 栏目vo
     */
    @TableField("column_map")
    private Map<Integer, ColumnVo> columnMap;


}
