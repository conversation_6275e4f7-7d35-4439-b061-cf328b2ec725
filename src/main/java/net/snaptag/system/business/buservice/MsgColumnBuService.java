package net.snaptag.system.business.buservice;

import net.snaptag.system.business.cache.MsgColumnCacheService;
import net.snaptag.system.business.cache.SystemMsgCacheBuService;
import net.snaptag.system.business.dao.MsgCenterDao;
import net.snaptag.system.business.dao.MsgColumnDao;
import net.snaptag.system.business.dto.MsgColumnDto;
import net.snaptag.system.business.dto.SaveMsgColumnDto;
import net.snaptag.system.business.entity.MsgCenter;
import net.snaptag.system.business.entity.MsgColumn;
import net.snaptag.system.business.enums.MsgColumnTypeEnums;
import net.snaptag.system.business.utils.ToolUtils;
import net.snaptag.system.business.vo.ColumnVo;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.core.I18nUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 消息栏目服务类
 * 
 * <AUTHOR> 2018年7月10日
 */
@Service
public class MsgColumnBuService {
    @Autowired
    private MsgColumnDao msgColumnDao;
    @Autowired
    private MsgColumnCacheService msgColumnCacheService;
    @Autowired
    private SystemMsgCacheBuService systemMsgCacheBuService;
    @Autowired
    private CommonProperties commonProperties;
    @Autowired
    private I18nUtils i18nUtils;
    @Autowired
    private MsgCenterDao msgCenterDao;

    /**
     * 保存
     * 
     * @param msgColumn
     */
    public void save(MsgColumn msgColumn) {
        msgColumnDao.saveOrUpdate(msgColumn);
        msgColumnCacheService.saveMsgColumn(msgColumn);
    }

    /**
     * 获取消息信息
     * 
     * @param userId
     *            用户ID
     * @return 消息信息
     * @throws ServiceException
     */
    private MsgColumn getMsgColumnByUserId(String userId) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("userId不能为空");
        }
        MsgColumn msgColumn = msgColumnCacheService.getMsgColumn(userId);
        if (ToolsKit.isEmpty(msgColumn)) {
            try {
                msgColumn = msgColumnDao.getMsgColumnByUserId(userId);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(msgColumn)) {
                msgColumnCacheService.saveMsgColumn(msgColumn);
            }
        }
        return msgColumn;
    }

    /**
     * 获取默认消息栏目
     * 
     * @param userId
     *            用户ID
     * @return
     */
    private MsgColumn getDefaultMsgColumn(String userId) {
        MsgColumn msgColumn = new MsgColumn();
        ToolsKit.setIdEntityData(msgColumn, userId);
        msgColumn.setUserId(userId);
        Map<Integer, ColumnVo> columnMap = new HashMap<Integer, ColumnVo>();
        Date now = new Date();
        for (MsgColumnTypeEnums msgColumnTypeEnums : MsgColumnTypeEnums.values()) {
            ColumnVo vo = new ColumnVo();
            vo.setName(msgColumnTypeEnums.getDesc());
            vo.setType(msgColumnTypeEnums.getType());
            vo.setCount(0);
            vo.setLastDate(now);
            columnMap.put(msgColumnTypeEnums.getType(), vo);
        }
        msgColumn.setColumnMap(columnMap);
        return msgColumn;
    }

    /**
     * 获取消息信息
     * 
     * @param userId
     *            用户ID
     * @return
     */
    public MsgColumn getMsgColumn(String userId) {
        MsgColumn msgColumn = this.getMsgColumnByUserId(userId);
        if (ToolsKit.isEmpty(msgColumn)) {
            msgColumn = this.getDefaultMsgColumn(userId);
            this.save(msgColumn);
        }
        // 添加新栏目
        this.addNewColumn(msgColumn);
        return msgColumn;
    }

    /**
     * 添加新栏目
     * 
     * @param msgColumn
     */
    private void addNewColumn(MsgColumn msgColumn) {
        Map<Integer, ColumnVo> columnMap = msgColumn.getColumnMap();
        if (!columnMap.containsKey(MsgColumnTypeEnums.SHARE.getType())) {
            ColumnVo vo = new ColumnVo();
            vo.setName(MsgColumnTypeEnums.SHARE.getDesc());
            vo.setType(MsgColumnTypeEnums.SHARE.getType());
            vo.setCount(0);
            vo.setLastDate(new Date());
            columnMap.put(MsgColumnTypeEnums.SHARE.getType(), vo);
        }
        msgColumn.setColumnMap(columnMap);
        this.save(msgColumn);
    }

    /**
     * 获取小铃铛消息
     * 
     * @param userId
     *            用户ID
     * @return
     * @throws ServiceException
     */
    public Map<String, Integer> getMsgCount(String userId, Locale locale, String version) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("userId不能为空");
        }
        Map<String, Integer> map = new HashMap<String, Integer>();
        List<MsgColumnDto> dtoList = this.getMsgColumnInfo(userId, false, locale, version);
        int hasCount = ToolsConst.STATUS_0;
        if (ToolsKit.isNotEmpty(dtoList)) {
            for (MsgColumnDto dto : dtoList) {
                // 不包括官方消息
                if (dto.getType() == MsgColumnTypeEnums.FEEDBACK.getType()) {
                    continue;
                }
                if (dto.getCount() > 0) {
                    hasCount = ToolsConst.STATUS_1;
                    break;
                }
            }
        }

//        MsgNotesDto msgNotesDto = null;
//        // 处理消息总数量 Update by RabyGao 2019-11-15
//        // 2.1.0 版本开始，消息总数量 = 小纸条数量 + 关注消息数量
////        if (StringUtils.isNotBlank(version) && (Integer.parseInt(version.replace(".", "")) >= Constant.APP_VERSION_210)) {
//        msgNotesDto = userNotesService.getMsgNotesListSize(userId, version);
//
//        if (msgNotesDto.getCount() > 0) {
//            hasCount = ToolsConst.STATUS_1;
//        }
        map.put("hasCount", hasCount);
        return map;
    }

    /**
     * 获取消息统计：
     * 1.个人主页：粉丝消息FOLLOWS
     * 2.消息中心：社区动态COMMUNITY, 系统公告SYSTEM, 官方消息FEEDBACK，互动打印SHARE
     *
     * 官方消息(用于接收来自支撑平台的反馈回复消息)
     * @param userId
     * @param version
     * @return
     * @throws ServiceException
     */
    public Map<String, Integer> getMsgCountStat(String userId, Locale locale, String version) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("userId不能为空");
        }
        if (ToolsKit.isEmpty(version)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("version不能为空");
        }
        Map<String, Integer> map = new HashMap<String, Integer>();
        List<MsgColumnDto> dtoList = this.getMsgColumnInfo(userId, false, locale, version);
        int homeCount = ToolsConst.STATUS_0;
        int centerCount = ToolsConst.STATUS_0;
        if (ToolsKit.isNotEmpty(dtoList)) {
            for (MsgColumnDto dto : dtoList) {
                if (dto.getCount() > 0) {
                    if (dto.getType() == MsgColumnTypeEnums.FOLLOWS.getType()) {
                        homeCount = ToolsConst.STATUS_1;
                    } else {
//                        centerCount = ToolsConst.STATUS_1;
                        centerCount += dto.getCount();
                    }
                }
            }
            // 4.0.0版本后，此处
        }

//        if (homeCount == ToolsConst.STATUS_0) {
//            MsgNotesDto msgNotesDto = null;
//            // 处理消息总数量 Update by RabyGao 2019-11-15
//            // 2.1.0 版本开始，消息总数量 = 小纸条数量 + 关注消息数量
////            if (StringUtils.isNotBlank(version) && (Integer.parseInt(version.replace(".", "")) >= Constant.APP_VERSION_210)) {
//            msgNotesDto = userNotesService.getMsgNotesListSize(userId, version);
//            if (msgNotesDto.getCount() > 0) {
//                homeCount = ToolsConst.STATUS_1;
//            }
//        }

        // 消息中心消息数量，如果上面的为空，此处再次寻找：系统消息、用户纸条信息
        // 反馈信息
//        if (centerCount==ToolsConst.STATUS_0){
//            int feedbackCount = msgCenterBuService.hasUnreadMsgCount(userId);
//            if (feedbackCount>0){
//                centerCount = ToolsConst.STATUS_1;
//            }
//        }
//        int feedbackCount = msgCenterBuService.hasUnreadMsgCount(userId);
//        if (feedbackCount>0){
//            centerCount += feedbackCount;
//        }
        // 用户纸条信息
//        if (centerCount==ToolsConst.STATUS_0){
//            List<UserNotesDto> list = userNotesService.getMsgNotesList(userId);
//            if (ToolsKit.isNotEmpty(list)){
//                for (UserNotesDto userNotes : list) {
//                    if (userNotes.getHasMsg()>0){
//                        centerCount = ToolsConst.STATUS_1;
//                    }
//                }
//            }
//        }

//        List<UserNotesDto> list = userNotesService.get(userId);
//        System.out.println(list);
//        if (ToolsKit.isNotEmpty(list)){
//            System.out.println(list.size());
//            for (UserNotesDto userNotes : list) {
//                centerCount += userNotes.getCount();
//                if (userNotes.getHasMsg()>0){
//                    centerCount += userNotes.getCount();
//                }
//            }
//        }
//        centerCount += userNotesService.getUserNotesUnreadCount(userId, version);
        // 个人主页消息数量
        map.put("homeCount", homeCount);
        // 消息中心消息数量
        map.put("centerCount", centerCount);
        return map;
    }

    /**
     * 获取消息栏目信息
     * 
     * @param userId
     *            用户ID
     * @return
     * @throws ServiceException
     */
    public List<MsgColumnDto> getMsgColumnInfo(String userId, boolean isFilterShare, Locale locale, String version) throws ServiceException {
        List<Integer> typeList = new ArrayList<Integer>();
//        int currVer = Integer.parseInt(version.replace(".", ""));
        for (MsgColumnTypeEnums msgColumnTypeEnums : MsgColumnTypeEnums.values()) {
            // 处理版本过期 Update by RabyGao 2019-11-15
            if (StringUtils.isNotBlank(msgColumnTypeEnums.getMinVersion())) {
                // 限定最低版本
                int minVer = Integer.parseInt(msgColumnTypeEnums.getMinVersion().replace(".", ""));
//                if (currVer < minVer) {
//                    continue;
//                }
                if (!ToolUtils.compareVersion(msgColumnTypeEnums.getMinVersion(), version)){
                    continue;
                }
            }
            if (StringUtils.isNotBlank(msgColumnTypeEnums.getMaxVersion())) {
                // 限定最高版本
                int maxVer = Integer.parseInt(msgColumnTypeEnums.getMaxVersion().replace(".", ""));
//                if (currVer > maxVer) {
//                    continue;
//                }
                if (ToolUtils.compareVersion(msgColumnTypeEnums.getMaxVersion(), version)){
                    continue;
                }
            }

            typeList.add(msgColumnTypeEnums.getType());
        }

        // 4.0.0版本以后
        // 消息中心：获赞信息，评论信息, 系统公告SYSTEM
        List<Integer> sortList = new ArrayList<>();
        sortList.add(MsgColumnTypeEnums.GIVELIKE.getType());
        sortList.add(MsgColumnTypeEnums.COMMENT.getType());
        sortList.add(MsgColumnTypeEnums.SYSTEM.getType());

        List<MsgColumnDto> msgColumns = new ArrayList<>();

        List<MsgColumnDto> msgColList = this.getMsgColumnByType(userId, sortList, false, locale);
        for (Integer type:sortList) {
            MsgColumnDto msgColumnDto = new MsgColumnDto();
            // modify by linnt:2020-07-30  以上屏蔽掉从数据库里面取栏目信息，此栏目信息在之后的版本中已固化，非配置
            msgColumnDto.setType(type);

            String name = i18nUtils.getKey(MsgColumnTypeEnums.getMsgColumnMap(type).getDesc(),locale);
            if (ToolsKit.isEmpty(name)){
                name = MsgColumnTypeEnums.getMsgColumnMap(type).getName();
            }
            msgColumnDto.setName(name);
            msgColumnDto.setCount(hasUnreadMsgCount(userId, type));
            msgColumnDto.setPic(MsgColumnTypeEnums.getMsgColumnMap(type).getPic());
            msgColumnDto.setSort(MsgColumnTypeEnums.getMsgColumnMap(type).getSort());

            // 特殊处理
            if (type == MsgColumnTypeEnums.FEEDBACK.getType()){
                msgColumnDto.setCount(hasUnreadMsgCount(userId, type));
                msgColumnDto.setLastDate(new Date());
            } else if (type == MsgColumnTypeEnums.SYSTEM.getType()) {
                msgColumnDto.setPic(msgColumnDto.getPic().replaceAll("me_ic_systemnotice", "me_ic_xx_systemnotice"));
            }

            // 设置它的count
            for (MsgColumnDto msgCol:msgColList) {
                if (msgCol.getType() == type) {
                    msgColumnDto.setCount(msgCol.getCount());
                    msgColumnDto.setLastDate(msgCol.getLastDate());
                }
            }
            msgColumns.add(msgColumnDto);
        }
        return msgColumns;

    }

    /**
     * 获取消息栏目信息
     *
     * @param userId
     *            用户ID
     * @return
     * @throws ServiceException
     */
    public MsgColumnDto getMsgColumnInfoFeedback(String userId, Locale locale) throws ServiceException {
        List<MsgCenter> list = msgCenterDao.findMsgCenterList(userId, MsgColumnTypeEnums.FEEDBACK.getType(), 0, 10000, null);
        if(ToolsKit.isEmpty(list) || list.isEmpty() || list.size()==0){

            MsgColumnDto msgColumnDto = new MsgColumnDto();
            msgColumnDto.setType(MsgColumnTypeEnums.FEEDBACK.getType());
            msgColumnDto.setName(MsgColumnTypeEnums.FEEDBACK.getName());
            msgColumnDto.setCount(0);
            msgColumnDto.setLastDate(new Date());
            msgColumnDto.setPic(MsgColumnTypeEnums.FEEDBACK.getPic());
            msgColumnDto.setSort(MsgColumnTypeEnums.FEEDBACK.getSort());

            return msgColumnDto;
        }
//        msgCenterDao.findUnReadMsgCenterList(userId, Integer.parseInt(msgType));
        MsgColumnDto msgColumnDto = new MsgColumnDto();
        msgColumnDto.setType(MsgColumnTypeEnums.FEEDBACK.getType());
        msgColumnDto.setName(MsgColumnTypeEnums.FEEDBACK.getName());
        msgColumnDto.setCount(hasUnreadMsgCount(userId, MsgColumnTypeEnums.FEEDBACK.getType()));
        msgColumnDto.setLastDate(list.get(0).getCreatetime());
        msgColumnDto.setPic(MsgColumnTypeEnums.FEEDBACK.getPic());
        msgColumnDto.setSort(MsgColumnTypeEnums.FEEDBACK.getSort());
        return msgColumnDto;
    }

    /**
     * 获取消息栏目信息
     *
     * @param userId
     *            用户ID
     * @return
     * @throws ServiceException
     */
    public MsgColumnDto getMsgColumnInfoOffical(String userId, Locale locale) throws ServiceException {
        List<MsgCenter> list = msgCenterDao.findMsgCenterList(userId, MsgColumnTypeEnums.OFFICIAL.getType(),0,10, null);

        if(ToolsKit.isEmpty(list) || list.isEmpty() || list.size()==0){
            return null;
        }
        MsgColumnDto msgColumnDto = new MsgColumnDto();
        msgColumnDto.setType(MsgColumnTypeEnums.OFFICIAL.getType());
        msgColumnDto.setName(MsgColumnTypeEnums.OFFICIAL.getName());
        msgColumnDto.setCount(hasUnreadMsgCount(userId, MsgColumnTypeEnums.OFFICIAL.getType()));
        msgColumnDto.setLastDate(list.get(0).getCreatetime());
        msgColumnDto.setPic(MsgColumnTypeEnums.OFFICIAL.getPic());
        msgColumnDto.setSort(MsgColumnTypeEnums.OFFICIAL.getSort());
        return msgColumnDto;
    }

    /**
     * 获取消息栏目信息
     * 
     * @param userId
     *            用户ID
     * @return
     * @throws ServiceException
     */
    public List<MsgColumnDto> getMsgColumnByType(String userId, List<Integer> typeList, boolean isFilterShare, Locale locale) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("userId不能为空");
        }
        if (ToolsKit.isEmpty(typeList)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("栏目类型不能为空");
        }
        MsgColumn msgColumn = this.getMsgColumn(userId);
        List<MsgColumnDto> dtoList = new ArrayList<MsgColumnDto>();
        try {
            boolean isUpdate = false;
            Map<Integer, ColumnVo> columnMap = msgColumn.getColumnMap();
            for (Integer type : typeList) {
                if (columnMap.containsKey(type)) {
                    ColumnVo columnVo = columnMap.get(type);
                    if (isFilterShare && columnVo.getType() == MsgColumnTypeEnums.SHARE.getType()) {
                        continue;
                    }
                    if (columnVo.getType() == MsgColumnTypeEnums.SYSTEM.getType()) {// 系统消息类型的去读取最新的消息
                        int count = systemMsgCacheBuService.getSystemMsgCount(columnVo.getLastDate());
                        if (count > 0) {// 大于最后阅读时间的，证明有新的系统消息未看过
                            columnVo.setCount(count); // columnVo.setCount(columnVo.getCount() + count);
                            columnVo.setRefreshDate(new Date());
                            msgColumn.getColumnMap().put(columnVo.getType(), columnVo);
                            isUpdate = true;
                        }
                    }
                    MsgColumnDto msgColumnDto = new MsgColumnDto();
                    ToolsKit.Bean.copyProperties(columnVo, msgColumnDto);
                    for (MsgColumnTypeEnums msgColumnTypeEnums : MsgColumnTypeEnums.values()) {
                        if (msgColumnTypeEnums.getType() == columnVo.getType()) {
                            msgColumnDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), msgColumnTypeEnums.getPic()));
                            msgColumnDto.setSort(msgColumnTypeEnums.getSort());
                            String name = i18nUtils.getKey(msgColumnTypeEnums.getDesc(), locale);
                            msgColumnDto.setName(name);
                        }
                    }
                    dtoList.add(msgColumnDto);
                }
            }
            if (isUpdate) {
                this.save(msgColumn);
            }
            Collections.sort(dtoList, new Comparator<MsgColumnDto>() {
                @Override
                public int compare(MsgColumnDto o1, MsgColumnDto o2) {
                    return o2.getSort() - o1.getSort();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dtoList;
    }

    /**
     * 更新最后更新时间和数量
     * 
     * @param saveMsgColumnDto
     * @throws ServiceException
     */
    public void saveMsgColumnByType(SaveMsgColumnDto saveMsgColumnDto) throws ServiceException {
        if (ToolsKit.isEmpty(saveMsgColumnDto.getUserId())) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("userId不能为空");
        }
        MsgColumn msgColumn = this.getMsgColumn(saveMsgColumnDto.getUserId());
        Map<Integer, ColumnVo> columnMap = msgColumn.getColumnMap();
        for (Map.Entry<Integer, ColumnVo> entry : columnMap.entrySet()) {
            ColumnVo columnVo = entry.getValue();
            if (saveMsgColumnDto.getType() == columnVo.getType()) {
                boolean isUpdate = false;
                if (saveMsgColumnDto.getCount() > -1) {
                    columnVo.setCount(saveMsgColumnDto.getCount());
                    isUpdate = true;
                }
                if (ToolsKit.isNotEmpty(saveMsgColumnDto.getLasdDate())) {
                    columnVo.setLastDate(saveMsgColumnDto.getLasdDate());
                    isUpdate = true;
                }
                if (isUpdate) {
                    columnMap.put(saveMsgColumnDto.getType(), columnVo);
                    msgColumn.setColumnMap(columnMap);
                    this.save(msgColumn);
                }
            }
        }
    }

    /**
     * 重置栏目未读数
     * 
     * @param userId
     *            用户ID
     * @param type
     *            栏目类型
     */
    public void resetCount(String userId, int type) {
        MsgColumn msgColumn = this.getMsgColumn(userId);
        Map<Integer, ColumnVo> columnMap = msgColumn.getColumnMap();
        ColumnVo columnVo = columnMap.get(type);
        if (ToolsKit.isNotEmpty(columnVo)) {
            columnVo.setCount(0);
            columnMap.put(type, columnVo);
            msgColumn.setColumnMap(columnMap);
            this.save(msgColumn);
        }
    }

    /**
     * 添加消息数
     * 
     * @param userId
     *            用户ID
     * @param type
     *            消息类型
     * @param count
     *            数量
     */
    public void addCount(String userId, int type, int count) {
        System.out.println("----------------------------------");
        try {
            System.out.println("userId=" + userId);
            MsgColumn msgColumn = this.getMsgColumn(userId);
            Map<Integer, ColumnVo> map = msgColumn.getColumnMap();
            ColumnVo columnVo = map.get(type);
            if (ToolsKit.isNotEmpty(columnVo)) {
                columnVo.setCount(columnVo.getCount() + count);
                map.put(type, columnVo);
                msgColumn.setColumnMap(map);
            } else {
                columnVo = new ColumnVo();
                MsgColumnTypeEnums enums = MsgColumnTypeEnums.getMsgColumnMap(type);
                if (enums==null) {
                    throw new ServiceException("错误的消息类型");
                }
                columnVo.setCount(count);
                columnVo.setLastDate(new Date());
                columnVo.setName(enums.getDesc());
                columnVo.setType(type);
                columnVo.setRefreshDate(new Date());
                map.put(type, columnVo);
                msgColumn.setColumnMap(map);
            }
            if (MsgColumnTypeEnums.COMMENT.getType() == type
                    || MsgColumnTypeEnums.GIVELIKE.getType() == type){
                ColumnVo columnVox = map.get(MsgColumnTypeEnums.COMMUNITY.getType());
                if(columnVox==null) {
                    columnVox = new ColumnVo();
                    columnVox.setCount(1);
                    columnVox.setLastDate(new Date());
                    columnVox.setName(MsgColumnTypeEnums.COMMUNITY.getDesc());
                    columnVox.setType(type);
                    columnVox.setRefreshDate(new Date());
                    map.put(MsgColumnTypeEnums.COMMUNITY.getType(), columnVox);
                    msgColumn.setColumnMap(map);
                } else {
                    columnVox.setCount(columnVox.getCount() + count);
                    map.put(MsgColumnTypeEnums.COMMUNITY.getType(), columnVox);
                    msgColumn.setColumnMap(map);
                }
            }
            this.save(msgColumn);
        } catch (Exception e){
            e.printStackTrace();
        }

        System.out.println("----------------------------------");
    }

    private int hasUnreadMsgCount(String userId, int type) {
        Long count = msgCenterDao.countMsgCenter(userId, type, ToolsConst.STATUS_0);
        return count.intValue();
    }

}
