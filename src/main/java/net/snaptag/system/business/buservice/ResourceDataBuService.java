package net.snaptag.system.business.buservice;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.account.buservice.UserAccountAndInfoBuService;
import net.snaptag.system.account.buservice.UserAccountBuService;
import net.snaptag.system.account.buservice.UserInfoBuService;
import net.snaptag.system.account.dto.UserLoginDto;
import net.snaptag.system.business.cache.ResourceDataCacheService;
import net.snaptag.system.business.dao.ResourceDataDao;
import net.snaptag.system.business.entity.ResourceData;
import net.snaptag.system.business.enums.ResourceTypeEnums;
import net.snaptag.system.business.vo.PicVo;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.common.WebKit;
import net.snaptag.system.sadais.web.config.CommonProperties;
import net.snaptag.system.sadais.web.dto.PicInfoDto;
import net.snaptag.system.business.dto.ResourceDataDto;
import net.snaptag.system.business.dto.PicDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ResourceDataBuService {
    @Autowired
    private ResourceDataDao resourceDataDao;
    @Autowired
    private ResourceDataCacheService resourceDataCacheService;
    @Autowired
    private CommonProperties         commonProperties;
    @Autowired
    private UserAccountAndInfoBuService userAccountAndInfoBuServiced;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为PicVo
     */
    private PicVo parseResUrlJson(String resUrlJson) {
        if (resUrlJson == null || resUrlJson.trim().isEmpty()) {
            return new PicVo();
        }
        try {
            return objectMapper.readValue(resUrlJson, PicVo.class);
        } catch (Exception e) {
            // 如果解析失败，返回空PicVo
            return new PicVo();
        }
    }

    /**
     * 将PicVo转换为JSON字符串
     */
    private String picVoToResUrlJson(PicVo picVo) {
        if (picVo == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(picVo);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 根据ID获取资源对象
     * 
     * @param id
     *            ID
     * @return 资源对象
     * @throws ServiceException
     */
    public ResourceData getResourceDataById(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        ResourceData resourceData = resourceDataCacheService.getResourceDataById(id);
        if (ToolsKit.isEmpty(resourceData)) {
            try {
                resourceData = resourceDataDao.getResourceDataById(id);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (ToolsKit.isNotEmpty(resourceData)) {
                resourceDataCacheService.saveResourceData(resourceData);
            }
        }
        return resourceData;
    }

    /**
     * 获取资源数据
     * 
     * @param id
     *            记录ID
     * @return
     * @throws ServiceException
     */
    public ResourceDataDto getResourceData(String id) throws ServiceException {
        if (ToolsKit.isEmpty(id)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("记录信息ID不能为空");
        }
        ResourceData resourceData = this.getResourceDataById(id);
        if (ToolsKit.isNotEmpty(resourceData)) {
            ResourceDataDto dto = new ResourceDataDto();
            UserLoginDto userLoginDto = userAccountAndInfoBuServiced.getLoginInfo(resourceData.getUserId());
            ToolsKit.Bean.copyProperties(resourceData, dto);
            if (ToolsKit.isNotEmpty(resourceData.getResUrl())) {
                PicVo picVo = parseResUrlJson(resourceData.getResUrl());
                PicDto picDto = new PicDto();
                ToolsKit.Bean.copyProperties(picVo, picDto);
                picDto.setPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), picVo.getPic()));
                dto.setResUrl(picDto);
            }
            if (ToolsKit.isNotEmpty(userLoginDto) && ToolsKit.isNotEmpty(userLoginDto.getUserInfoDto())) {
                dto.setNickName(userLoginDto.getUserInfoDto().getNickName());
                dto.setUserPic(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), userLoginDto.getUserInfoDto().getUserPic()));
            }
            dto.setCreateDate(ToolsKit.Date.format(resourceData.getCreatetime(), "yyyy年MM月dd") + "日");
            return dto;
        }
        return null;
    }

    public void save(ResourceData resourceData) {
        resourceDataDao.saveOrUpdate(resourceData);
        resourceDataCacheService.saveResourceData(resourceData);
    }

    /**
     * 保存资源信息
     * 
     * @param userId
     *            用户ID
     * @param resUrl
     *            资源地址
     * @param resType
     *            资源类型
     * @return
     */
    public ResourceDataDto saveResource(String userId, String resUrl, int resType, String content) throws ServiceException {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("用户ID不能为空");
        }
        // if (ToolsKit.isEmpty(resUrl)) {
        // throw new ServiceException().setCode(ExceptionEnums.PARAM_ERROR.getCode()).setMessage("资源地址不能为空");
        // }
        ResourceData resourceData = new ResourceData();
        ToolsKit.setIdEntityData(resourceData, userId);
        resourceData.setUserId(userId);
        if (ToolsKit.isNotEmpty(resUrl)) {
            resUrl = ToolsKit.URL.decode(resUrl, ToolsConst.DEFAULT_ENCODING);
            String tempPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), resUrl);
            if (!ToolsKit.URL.isUrl(tempPic) && !tempPic.startsWith("/")) {
                tempPic = "/" + tempPic;
            }
            PicVo picVo = new PicVo();
            picVo.setPic(tempPic);
            if (resType == ResourceTypeEnums.PIC.getValue()||resType == ResourceTypeEnums.SHARE.getValue()) {
                PicInfoDto picInfoDto = WebKit.getPicInfo(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), resUrl));
                if (ToolsKit.isNotEmpty(picInfoDto)) {
                    picVo.setHeight(picInfoDto.getImageHeight());
                    picVo.setWidth(picInfoDto.getImageWidth());
                }
            }
            resourceData.setResUrl(picVoToResUrlJson(picVo));
        }
        resourceData.setResType(resType);
        resourceData.setContent(content);
        this.save(resourceData);
        ResourceDataDto dto = new ResourceDataDto();
        ToolsKit.Bean.copyProperties(resourceData, dto);
        return dto;
    }

    /**
     * 更新资源地址
     * 
     * @param id
     *            资源ID
     * @param resUrl
     *            资源地址
     */
    public void updateResource(String id, String resUrl, int type) {
        ResourceData resourceData = this.getResourceDataById(id);
        if (ToolsKit.isNotEmpty(resourceData)) {
            PicVo picVo = parseResUrlJson(resourceData.getResUrl());
            if (type != ResourceTypeEnums.PIC.getValue()) {
                picVo.setPic(resUrl);
            } else {
                PicInfoDto picInfoDto = WebKit.getPicInfo(ToolsKit.URL.getUrlByServer(commonProperties.getFileDomain(), resUrl));
                String tempPic = ToolsKit.URL.replaceUrl(commonProperties.getFileDomain(), resUrl);
                if (!ToolsKit.URL.isUrl(tempPic) && !tempPic.startsWith("/")) {
                    tempPic = "/" + tempPic;
                }
                picVo.setPic(tempPic);
                if (ToolsKit.isNotEmpty(picInfoDto)) {
                    picVo.setHeight(picInfoDto.getImageHeight());
                    picVo.setWidth(picInfoDto.getImageWidth());
                }
            }
            resourceData.setResUrl(picVoToResUrlJson(picVo));
            this.save(resourceData);
        }
    }

    /**
     * 删除资源信息
     * 
     * @param id
     */
    public void delResource(String id) {
        ResourceData resourceData = this.getResourceDataById(id);
        if (ToolsKit.isNotEmpty(resourceData)) {
            resourceData.setStatus(ToolsConst.DATA_DELETE_STATUS);
            resourceDataDao.saveOrUpdate(resourceData);
            resourceDataCacheService.removeResourceDataById(id);
        }
    }
}
