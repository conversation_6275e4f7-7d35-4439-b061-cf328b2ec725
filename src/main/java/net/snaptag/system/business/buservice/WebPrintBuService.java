package net.snaptag.system.business.buservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.business.dao.WebPrintDao;
import net.snaptag.system.business.entity.WebPrint;
import net.snaptag.system.business.enums.WebPrintDefaultEnums;
import net.snaptag.system.business.vo.WebPagePrintVo;
import net.snaptag.system.business.vo.WebPrintGroupVo;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网页打印业务服务类
 * 
 * <AUTHOR> 2019年11月05日
 * @restored 2025年01月30日 - 恢复被删除的文件
 */
@Service
public class WebPrintBuService {
    
    @Autowired
    private WebPrintDao webPrintDao;
    
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为List<WebPagePrintVo>
     */
    private List<WebPagePrintVo> parsePageListJson(String pageListJson) {
        if (pageListJson == null || pageListJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(pageListJson, new TypeReference<List<WebPagePrintVo>>() {});
        } catch (Exception e) {
            // 如果解析失败，返回空List
            return new ArrayList<>();
        }
    }

    /**
     * 将List<WebPagePrintVo>转换为JSON字符串
     */
    private String pageListToJson(List<WebPagePrintVo> pageList) {
        if (pageList == null || pageList.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(pageList);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 获取默认分组列表
     * @return
     */
    public List<WebPrintGroupVo> getDefaultGroupList() {
        List<WebPrint> webPrintList = webPrintDao.findDefaultGroupList();
        List<WebPrintGroupVo> result = new ArrayList<>();
        
        // 添加系统默认分组
        result.add(WebPrintDefaultEnums.getDefaultWebPrintGroup());
        
        // 转换数据库中的默认分组
        for (WebPrint webPrint : webPrintList) {
            WebPrintGroupVo groupVo = convertToGroupVo(webPrint);
            result.add(groupVo);
        }
        
        return result;
    }

    /**
     * 获取指定用户的分组列表
     * @param userId - 用户标识
     * @return
     */
    public List<WebPrintGroupVo> getUserGroupList(String userId) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        
        List<WebPrint> webPrintList = webPrintDao.findUserGroupList(userId);
        List<WebPrintGroupVo> result = new ArrayList<>();
        
        // 转换用户分组
        for (WebPrint webPrint : webPrintList) {
            WebPrintGroupVo groupVo = convertToGroupVo(webPrint);
            result.add(groupVo);
        }
        
        return result;
    }

    /**
     * 获取指定用户的分组
     * @param userId - 用户标识
     * @param groupId - 分组标识
     * @return
     */
    public WebPrintGroupVo getUserGroup(String userId, String groupId) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(groupId)) {
            throw new ServiceException("分组ID不能为空");
        }
        
        WebPrint webPrint = webPrintDao.findUserGroup(userId, groupId);
        if (webPrint == null) {
            return null;
        }
        
        return convertToGroupVo(webPrint);
    }

    /**
     * 保存或更新网页打印分组
     * @param webPrintGroupVo - 分组信息
     * @param userId - 用户ID
     * @return
     */
    public WebPrintGroupVo saveOrUpdateGroup(WebPrintGroupVo webPrintGroupVo, String userId) {
        if (webPrintGroupVo == null) {
            throw new ServiceException("分组信息不能为空");
        }
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        
        WebPrint webPrint = new WebPrint();
        
        // 如果有ID，则为更新操作
        if (ToolsKit.isNotEmpty(webPrintGroupVo.getId())) {
            webPrint = webPrintDao.getById(webPrintGroupVo.getId());
            if (webPrint == null) {
                throw new ServiceException("分组不存在");
            }
            ToolsKit.setIdEntityData(webPrint, userId);
        } else {
            // 新增操作
            ToolsKit.setIdEntityData(webPrint, userId);
            webPrint.setUserId(userId);
        }
        
        // 设置基本信息
        webPrint.setName(webPrintGroupVo.getName());
        webPrint.setIsDefault(webPrintGroupVo.getIsDefault());
        webPrint.setFromDefault(0); // 用户自定义分组
        
        // 转换页面列表为JSON
        String pageListJson = pageListToJson(webPrintGroupVo.getPageList());
        webPrint.setPageList(pageListJson);
        
        // 保存到数据库
        webPrintDao.saveOrUpdate(webPrint);
        
        // 返回转换后的VO
        return convertToGroupVo(webPrint);
    }

    /**
     * 删除分组
     * @param groupId - 分组ID
     * @param userId - 用户ID
     * @return
     */
    public String deleteGroup(String groupId, String userId) {
        if (ToolsKit.isEmpty(groupId)) {
            throw new ServiceException("分组ID不能为空");
        }
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        
        WebPrint webPrint = webPrintDao.getById(groupId);
        if (webPrint == null) {
            throw new ServiceException("分组不存在");
        }
        
        // 检查权限：只能删除自己的分组
        if (!userId.equals(webPrint.getUserId())) {
            throw new ServiceException("无权限删除此分组");
        }
        
        webPrintDao.removeById(groupId);
        return "删除成功";
    }

    /**
     * 分页查询分组列表
     * @param pageNum - 页码
     * @param pageSize - 页大小
     * @param userId - 用户ID（可选）
     * @return
     */
    public Page<WebPrintGroupVo> findPage(int pageNum, int pageSize, String userId) {
        Page<WebPrint> page = new Page<>(pageNum, pageSize);
        Page<WebPrint> resultPage = webPrintDao.page(page);
        
        // 转换为VO
        Page<WebPrintGroupVo> voPage = new Page<>(pageNum, pageSize);
        voPage.setTotal(resultPage.getTotal());
        voPage.setPages(resultPage.getPages());
        
        List<WebPrintGroupVo> voList = new ArrayList<>();
        for (WebPrint webPrint : resultPage.getRecords()) {
            voList.add(convertToGroupVo(webPrint));
        }
        voPage.setRecords(voList);
        
        return voPage;
    }

    /**
     * 将WebPrint实体转换为WebPrintGroupVo
     */
    private WebPrintGroupVo convertToGroupVo(WebPrint webPrint) {
        WebPrintGroupVo groupVo = new WebPrintGroupVo();
        groupVo.setId(webPrint.getId());
        groupVo.setName(webPrint.getName());
        groupVo.setIsDefault(webPrint.getIsDefault());
        
        // 解析页面列表JSON
        List<WebPagePrintVo> pageList = parsePageListJson(webPrint.getPageList());
        groupVo.setPageList(pageList);
        
        return groupVo;
    }

    /**
     * 复制默认分组给用户
     * @param userId - 用户ID
     * @param defaultGroupId - 默认分组ID
     * @return
     */
    public WebPrintGroupVo copyDefaultGroup(String userId, String defaultGroupId) {
        if (ToolsKit.isEmpty(userId)) {
            throw new ServiceException("用户ID不能为空");
        }
        if (ToolsKit.isEmpty(defaultGroupId)) {
            throw new ServiceException("默认分组ID不能为空");
        }
        
        WebPrint defaultGroup = webPrintDao.getById(defaultGroupId);
        if (defaultGroup == null || defaultGroup.getIsDefault() != 1) {
            throw new ServiceException("默认分组不存在");
        }
        
        // 创建新的用户分组
        WebPrint userGroup = new WebPrint();
        ToolsKit.setIdEntityData(userGroup, userId);
        userGroup.setUserId(userId);
        userGroup.setName(defaultGroup.getName());
        userGroup.setIsDefault(0); // 用户分组不是默认分组
        userGroup.setFromDefault(1); // 来自默认分组
        userGroup.setPageList(defaultGroup.getPageList());
        
        webPrintDao.save(userGroup);
        
        return convertToGroupVo(userGroup);
    }
}
