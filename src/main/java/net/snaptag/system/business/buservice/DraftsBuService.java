package net.snaptag.system.business.buservice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.snaptag.system.business.dao.DraftsDao;
import net.snaptag.system.business.entity.Drafts;
import net.snaptag.system.business.vo.DraftsParamVo;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 草稿箱业务服务类
 * 已修正JSON字段映射问题
 */
@Service
public class DraftsBuService {
    
    @Autowired
    private DraftsDao draftsDao;
    
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为DraftsParamVo
     */
    private DraftsParamVo parseDraftsParamJson(String draftsParamJson) {
        if (draftsParamJson == null || draftsParamJson.trim().isEmpty()) {
            return new DraftsParamVo();
        }
        try {
            return objectMapper.readValue(draftsParamJson, DraftsParamVo.class);
        } catch (Exception e) {
            // 如果解析失败，返回空DraftsParamVo
            return new DraftsParamVo();
        }
    }

    /**
     * 将DraftsParamVo转换为JSON字符串
     */
    private String draftsParamToJson(DraftsParamVo draftsParamVo) {
        if (draftsParamVo == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(draftsParamVo);
        } catch (Exception e) {
            // 如果转换失败，返回null
            return null;
        }
    }

    /**
     * 保存草稿
     */
    public void save(Drafts drafts) {
        if (drafts != null) {
            draftsDao.saveOrUpdate(drafts);
        }
    }

    /**
     * 根据ID获取草稿
     */
    public Drafts getById(String id) {
        if (ToolsKit.isEmpty(id)) {
            return null;
        }
        return draftsDao.getById(id);
    }

    /**
     * 删除草稿
     */
    public void deleteById(String id) {
        if (ToolsKit.isNotEmpty(id)) {
            draftsDao.removeById(id);
        }
    }

    /**
     * 根据用户ID获取草稿列表
     */
    public List<Drafts> getDraftsByUserId(String userId) {
        if (ToolsKit.isEmpty(userId)) {
            return new ArrayList<>();
        }
        QueryWrapper<Drafts> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("status", "审核通过");
        return draftsDao.list(wrapper);
    }

    /**
     * 根据用户ID和类型获取草稿列表
     */
    public List<Drafts> getDraftsByUserIdAndType(String userId, int type) {
        if (ToolsKit.isEmpty(userId)) {
            return new ArrayList<>();
        }
        QueryWrapper<Drafts> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId).eq("type", type);
        wrapper.eq("status", "审核通过");
        return draftsDao.list(wrapper);
    }

    /**
     * 保存草稿并处理参数
     */
    public Drafts saveDraftsWithParam(Drafts drafts, DraftsParamVo paramVo, String userId) {
        if (drafts == null) {
            return null;
        }

        // 设置基本信息
        if (ToolsKit.isEmpty(drafts.getId())) {
            ToolsKit.setIdEntityData(drafts, userId);
        } else {
            ToolsKit.setIdEntityData(drafts, userId);
        }

        // 转换参数为JSON
        String paramJson = draftsParamToJson(paramVo);
        drafts.setDraftsParam(paramJson);

        // 保存到数据库
        draftsDao.saveOrUpdate(drafts);

        return drafts;
    }

    /**
     * 获取草稿参数
     */
    public DraftsParamVo getDraftsParam(String id) {
        Drafts drafts = getById(id);
        if (drafts == null) {
            return null;
        }
        return parseDraftsParamJson(drafts.getDraftsParam());
    }
}
