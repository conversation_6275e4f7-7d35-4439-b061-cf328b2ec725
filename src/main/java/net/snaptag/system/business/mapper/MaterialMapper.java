package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.Material;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Material Mapper接口
 */
@Mapper
public interface MaterialMapper extends BaseMapper<Material> {

    /**
     * 分页查询Material列表
     */
    @Select("<script>" +
            "SELECT * FROM v1_material WHERE status = #{status}" +
            "<if test='name != null and name != \"\"'> AND name LIKE CONCAT('%', #{name}, '%')</if>" +
            "<if test='type != null'> AND type = #{type}</if>" +
            "<if test='subType != null'> AND sub_type = #{subType}</if>" +
            "<if test='pId != null and pId != \"\"'> AND p_id = #{pId}</if>" +
            " ORDER BY sort ASC, createtime DESC" +
            "</script>")
    IPage<Material> findMaterialPage(Page<Material> page, 
                                   @Param("name") String name, 
                                   @Param("type") Integer type, 
                                   @Param("subType") Integer subType,
                                   @Param("pId") String pId,
                                   @Param("status") String status);

    /**
     * 查询所有Material列表
     */
    @Select("SELECT * FROM v1_material WHERE status = #{status} ORDER BY sort ASC, createtime DESC")
    List<Material> findAllList(@Param("status") String status);

    /**
     * 根据类型查询Material列表
     */
    @Select("SELECT * FROM v1_material WHERE status = #{status} AND type = #{type} ORDER BY sort ASC")
    List<Material> findListByType(@Param("type") int type, @Param("status") String status);

    /**
     * 根据父ID查询子Material列表
     */
    @Select("SELECT * FROM v1_material WHERE status = #{status} AND p_id = #{pId} ORDER BY sort ASC")
    List<Material> findListByPId(@Param("pId") String pId, @Param("status") String status);
}
