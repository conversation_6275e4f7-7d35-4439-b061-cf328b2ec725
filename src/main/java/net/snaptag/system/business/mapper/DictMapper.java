package net.snaptag.system.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.snaptag.system.business.entity.Dict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Dict Mapper接口
 */
@Mapper
public interface DictMapper extends BaseMapper<Dict> {

    /**
     * 分页查询Dict列表
     */
    @Select("<script>" +
            "SELECT * FROM v1_dict WHERE status = #{status}" +
            "<if test='name != null and name != \"\"'> AND name LIKE CONCAT('%', #{name}, '%')</if>" +
            "<if test='type != null and type != \"\"'> AND type = #{type}</if>" +
            "<if test='localeCode != null and localeCode != \"\"'> AND locale_code = #{localeCode}</if>" +
            " ORDER BY sort_num ASC, createtime DESC" +
            "</script>")
    IPage<Dict> findDictPage(Page<Dict> page, 
                           @Param("name") String name, 
                           @Param("type") String type, 
                           @Param("localeCode") String localeCode,
                           @Param("status") String status);

    /**
     * 查询所有Dict列表
     */
    @Select("SELECT * FROM v1_dict WHERE status = #{status} ORDER BY sort_num ASC, createtime DESC")
    List<Dict> findAllList(@Param("status") String status);

    /**
     * 根据类型查询Dict列表
     */
    @Select("SELECT * FROM v1_dict WHERE status = #{status} AND type = #{type} ORDER BY sort_num ASC")
    List<Dict> findListByType(@Param("type") String type, @Param("status") String status);

    /**
     * 根据类型和语言代码查询Dict列表
     */
    @Select("SELECT * FROM v1_dict WHERE status = #{status} AND type = #{type} AND locale_code = #{localeCode} ORDER BY sort_num ASC")
    List<Dict> findListByTypeAndLocale(@Param("type") String type, @Param("localeCode") String localeCode, @Param("status") String status);
}
