package net.snaptag.system.business.dto;

import com.alibaba.fastjson.annotation.JSONField;
import net.snaptag.system.business.vo.PicVo;

import java.io.Serializable;
import java.util.Date;

public class DraftsDto implements Serializable {
    /**
     * 草稿箱Dto
     */
    private static final long serialVersionUID = 1L;
    private String            id;                   // 记录ID
    private String            pic;                  // 资源图片地址
    private String            picPrint;             // 直接打印的地址
    private String            data;                 // 资源数据地址
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date              createTime;           // 创建 时间
    private Object            param;                // 草稿箱参数
    private int               type;                 // 草稿箱类型 0--草稿箱 1--我的历史
    private int               placeType;            // 设置
    private PaperInfoDto      paperObj;
    private String            fmtTime;
    private PicVo               picVo;
    private String              printerType;            // 打印机型号
    private String            codeId;               // 用户的codeId
    private String              title;              // 标题
    private int                 isMirror;           // 是否镜像，0：非，1：是
    private String              materialColumnId;
    private String              previewPoint;
    private String              name;   // 标签名称

    public String getCodeId() {
        return codeId;
    }

    public void setCodeId(String codeId) {
        this.codeId = codeId;
    }

    public DraftsDto() {
    }

    public String getFmtTime() {
        return fmtTime;
    }

    public void setFmtTime(String fmtTime) {
        this.fmtTime = fmtTime;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Object getParam() {
        return param;
    }

    public void setParam(Object param) {
        this.param = param;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getPlaceType() {return placeType; }

    public void setPlaceType(int placeType) { this.placeType = placeType; }

    public PaperInfoDto getPaperObj() {
        return paperObj;
    }

    public void setPaperObj(PaperInfoDto paperObj) {
        this.paperObj = paperObj;
    }

    public PicVo getPicVo() {
        return picVo;
    }

    public void setPicVo(PicVo picVo) {
        this.picVo = picVo;
    }

    public String getPrinterType() {
        return printerType;
    }

    public void setPrinterType(String printerType) {
        this.printerType = printerType;
    }

    public String getPicPrint() {
        return picPrint;
    }

    public void setPicPrint(String picPrint) {
        this.picPrint = picPrint;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getIsMirror() {
        return isMirror;
    }

    public void setIsMirror(int isMirror) {
        this.isMirror = isMirror;
    }

    public String getMaterialColumnId() {
        return materialColumnId;
    }

    public void setMaterialColumnId(String materialColumnId) {
        this.materialColumnId = materialColumnId;
    }

    public String getPreviewPoint() {
        return previewPoint;
    }

    public void setPreviewPoint(String previewPoint) {
        this.previewPoint = previewPoint;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
