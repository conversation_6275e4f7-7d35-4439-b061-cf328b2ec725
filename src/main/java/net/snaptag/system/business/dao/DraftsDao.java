package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.Drafts;
import net.snaptag.system.business.entity.PrintPaperInfo;
import net.snaptag.system.business.enums.DraftsSubTypeEnums;
import net.snaptag.system.business.enums.PaperInfoEnums;
import net.snaptag.system.business.mapper.DraftsMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 草稿箱信息
 *
 * <AUTHOR> 2018年6月12日
 */
@Repository
public class DraftsDao extends ServiceImpl<DraftsMapper, Drafts> {

    @Autowired
    private DraftsMapper draftsMapper;
    /**
     * 获取草稿箱列表
     *
     * @return 草稿箱列表
     * @throws Exception
     */
    public List<Drafts> findDraftsList(String userId, int type, int subType,String materialColumnId, int length, int pageNo, int pageSize, String lastId, String printerType, List<PrintPaperInfo> printerPaperList, String paperType, String keyword) {
        QueryWrapper<Drafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("type", type);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(subType) && subType != DraftsSubTypeEnums.None.getValue()){
            queryWrapper.eq("sub_type", subType);
        }

        if (ToolsKit.isNotEmpty(materialColumnId)){
            queryWrapper.eq("material_column_id", materialColumnId);
        }

        if (ToolsKit.isNotEmpty(keyword)){
            queryWrapper.and(wrapper -> wrapper
                .like("title", keyword)
                .or().like("printer_type", keyword)
                .or().like("content", keyword)
            );
        }

        if (ToolsKit.isNotEmpty(paperType)){
            String first = paperType.charAt(0) + "";
            int max = (Integer.parseInt(first)+1) * 10;
            int min = Integer.parseInt(first) * 10;
            queryWrapper.between("paper_type", min, max);
        }

        if (ToolsKit.isNotEmpty(printerType)) {
            queryWrapper.eq("printer_type", printerType);
        }
        if (ToolsKit.isNotEmpty(lastId)) {
            queryWrapper.lt("id", lastId);
        }

        queryWrapper.orderByDesc("id");
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNo * pageSize));
        return this.list(queryWrapper);
    }


    /***
     * 用于3.3.0以下版本
     * @deprecated
     * @param userId
     * @param type
     * @param subType
     * @param length
     * @param pageNo
     * @param pageSize
     * @param lastId
     * @param printerType
     * @param paperType
     * @return
     */
    public List<Drafts> findDraftsList2(String userId, int type, int subType, int length, int pageNo, int pageSize, String lastId, String printerType, String paperType) {
        QueryWrapper<Drafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("type", type);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(lastId)) {
            queryWrapper.lt("id", lastId);
        }

        if (ToolsKit.isNotEmpty(paperType)){
            String first = paperType.charAt(0) + "";
            int max = (Integer.parseInt(first)+1) * 10;
            int min = Integer.parseInt(first) * 10;
            queryWrapper.between("paper_type", min, max);
        }

        if (ToolsKit.isNotEmpty(printerType)) {
            queryWrapper.eq("printer_type", printerType);
        }

        queryWrapper.orderByDesc("createtime");
        queryWrapper.select("id", "createtime");
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNo * pageSize));
        return this.list(queryWrapper);
    }

    /**
     * 获取草稿箱列表
     *
     * @return 草稿箱列表
     * @throws Exception
     */
    public IPage<Drafts> findDraftsPage(String userId, Integer type, int subType, Integer length, int pageNo, int pageSize, String lastId) {
        QueryWrapper<Drafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(userId)){
            queryWrapper.eq("user_id", userId);
        }

        if (ToolsKit.isNotEmpty(type)){
            queryWrapper.eq("type", type);
        }
        if (ToolsKit.isNotEmpty(length)){
            queryWrapper.eq("length", length);
        }

        if (ToolsKit.isNotEmpty(lastId)) {
            queryWrapper.lt("id", lastId);
        }

        queryWrapper.orderByDesc("createtime");
        queryWrapper.select("id", "createtime");
        Page<Drafts> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }



    /**
     * 根据ID获取草稿箱对象
     *
     * @return 草稿箱对象
     * @throws Exception
     */
    public Drafts getDraftsById(String id) {
        QueryWrapper<Drafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);
        return this.getOne(queryWrapper);
    }
}
