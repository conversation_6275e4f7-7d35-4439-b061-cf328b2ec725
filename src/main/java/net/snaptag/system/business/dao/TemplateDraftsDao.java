package net.snaptag.system.business.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.snaptag.system.business.entity.TemplateDrafts;
import net.snaptag.system.business.enums.PaperInfoEnums;
import net.snaptag.system.business.mapper.TemplateDraftsMapper;
import net.snaptag.system.common.DataConst;
import net.snaptag.system.sadais.util.core.ToolsKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021/9/28 11:05
 * @description：模板草稿DAO
 * @modified By：
 * @version: $
 */
@Repository
public class TemplateDraftsDao extends ServiceImpl<TemplateDraftsMapper, TemplateDrafts> {

    @Autowired
    private TemplateDraftsMapper templateDraftsMapper;
    public List<TemplateDrafts> findList(int pageNo, int pageSize, String type, int isHot, String localeCode, String printerType, String paperType, String length){
        QueryWrapper<TemplateDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(type)){
            queryWrapper.eq("type", type);
        }
        if (ToolsKit.isNotEmpty(localeCode)){
            queryWrapper.like("locale_code", localeCode);
        }
        if (isHot!=0){
            queryWrapper.eq("is_hot", isHot);
        }

        if (ToolsKit.isNotEmpty(length)){
            queryWrapper.like("printer_type", length);
        } else if (ToolsKit.isNotEmpty(printerType)){
            queryWrapper.like("printer_type", printerType);
        }

        if (ToolsKit.isNotEmpty(paperType)){
            String index = paperType.charAt(0) + "";
            int max = (Integer.parseInt(index) + 1) * 10;
            int min = Integer.parseInt(index) * 10;
            queryWrapper.between("paper_type", min, max);
        }

        queryWrapper.orderByDesc("sort_num");
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNo > 0 ? (pageNo - 1) * pageSize : 0));
        return this.list(queryWrapper);
    }

    public List<TemplateDrafts> findList(int pageNo, int pageSize, String type, int isHot, String localeCode, List<String> lens, String paperType, String length){
        QueryWrapper<TemplateDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(type)){
            queryWrapper.eq("type", type);
        }
        if (ToolsKit.isNotEmpty(localeCode)){
            queryWrapper.like("locale_code", localeCode);
        }
        if (isHot!=0){
            queryWrapper.eq("is_hot", isHot);
        }

        if (ToolsKit.isNotEmpty(length)){
            queryWrapper.like("paper_size", length);
        } else if (ToolsKit.isNotEmpty(lens)){
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < lens.size(); i++) {
                    if (i == 0) {
                        wrapper.like("paper_size", lens.get(i));
                    } else {
                        wrapper.or().like("paper_size", lens.get(i));
                    }
                }
            });
        }

        if (ToolsKit.isNotEmpty(paperType)){
            String index = paperType.charAt(0) + "";
            int max = (Integer.parseInt(index) + 1) * 10;
            int min = Integer.parseInt(index) * 10;
            queryWrapper.between("paper_type", min, max);
        }

        queryWrapper.orderByDesc("sort_num");
        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNo > 0 ? (pageNo - 1) * pageSize : 0));
        return this.list(queryWrapper);
    }

    public IPage<TemplateDrafts> findPage(int pageNo, int pageSize, String type, int isHot, String localeCode, String printerType, String paperType, String length){
        QueryWrapper<TemplateDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(type)){
            queryWrapper.eq("type", type);
        }
        if (ToolsKit.isNotEmpty(localeCode)){
            queryWrapper.like("locale_code", localeCode);
        }
        if (isHot!=0){
            queryWrapper.eq("is_hot", isHot);
        }

        if (ToolsKit.isNotEmpty(length)){
            queryWrapper.like("printer_type", length);
        } else if (ToolsKit.isNotEmpty(printerType)){
            queryWrapper.like("printer_type", printerType);
        }

        if (ToolsKit.isNotEmpty(paperType)){
            String index = paperType.charAt(0) + "";
            int max = (Integer.parseInt(index) + 1) * 10;
            int min = Integer.parseInt(index) * 10;
            queryWrapper.between("paper_type", min, max);
        }

        queryWrapper.orderByDesc("sort_num");
        Page<TemplateDrafts> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }

    public IPage<TemplateDrafts> findPage(int pageNo, int pageSize, String keyword, String localeCode, String printerType, String paperType){
        QueryWrapper<TemplateDrafts> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DataConst.DATA_SUCCESS_STATUS);

        if (ToolsKit.isNotEmpty(localeCode)){
            queryWrapper.like("locale_code", localeCode);
        }

        if (ToolsKit.isNotEmpty(printerType)){
            queryWrapper.like("printer_type", printerType);
        }

        if(ToolsKit.isNotEmpty(keyword)){
            queryWrapper.and(wrapper -> wrapper
                .like("name", keyword)
                .or().like("recommend", keyword)
                .or().like("content", keyword)
            );
        }

        if (ToolsKit.isNotEmpty(paperType)){
            queryWrapper.like("paper_type", paperType);
        }

        queryWrapper.orderByDesc("sort_num");
        Page<TemplateDrafts> page = new Page<>(pageNo + 1, pageSize);
        return this.page(page, queryWrapper);
    }
}
