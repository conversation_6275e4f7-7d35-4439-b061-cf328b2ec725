package net.snaptag.system.business.controller;

import com.alibaba.nacos.api.utils.StringUtils;
import net.snaptag.system.business.buservice.UserFeedbackBuService;
import net.snaptag.system.business.entity.UserFeedback;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.core.common.core.ToolsConst;
import net.snaptag.system.sadais.core.common.enums.ExceptionEnums;
import net.snaptag.system.sadais.core.common.exception.ServiceException;
import net.snaptag.system.sadais.util.core.ToolsKit;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.HeadInfoDto;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import net.snaptag.system.sadais.web.utils.WebTools;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 反馈控制器
 * 已修正JSON字段映射问题
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/feedback")
public class FeedBackController extends BaseController {
    
    @Autowired
    private UserFeedbackBuService userFeedbackBuService;

    /**
     * 获取用户的反馈列表
     * @return
     */
    @RequestMapping(value = "/getpagelist")
    public ReturnDto getPageList() {
        try {
            String startDate = this.getValue("startDate");
            String endDate = this.getValue("endDate");
            String pageNo = this.getValue("pageNo");
            String pageSize = this.getValue("pageSize");
            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userFeedbackBuService.findUserFeedbackPage(startDate, endDate, Integer.parseInt(pageNo)-1, Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 评论用户的反馈
     *
     * @return
     */
    @RequestMapping(value = "/update")
    public ReturnDto update() {
        try {
            String id = this.getValue("id");
            String remark = this.getValue("remark");
            userFeedbackBuService.update(id, remark);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除用户的反馈
     *
     * @return
     */
    @RequestMapping(value = "/delete")
    public ReturnDto delete() {
        try {
            String id = this.getValue("id");
            userFeedbackBuService.deleteById(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, null);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户历史反馈
     * @return
     */
    @RequestMapping(value = "/gethistories")
    public ReturnDto getHistories() {
        try {
            String userId = this.getValue("userid");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");

            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            if (StringUtils.isBlank(userId)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, new ArrayList<>());
            }

            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    userFeedbackBuService.getUserFeedbackList(userId, Integer.parseInt(pageNo), Integer.parseInt(pageSize)));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户历史反馈
     * @return
     */
    @RequestMapping(value = "/gethistories2")
    public ReturnDto getHistories2() {
        try {
            String userId = this.getValue("userid");
            String pageNo = this.getValue("pageno");
            String pageSize = this.getValue("pagesize");

            if (ToolsKit.isEmpty(pageNo)) {
                pageNo = "1";
            }
            if (ToolsKit.isEmpty(pageSize)) {
                pageSize = String.valueOf(ToolsConst.PHONEPAGESIZE);
            }
            if (StringUtils.isBlank(userId)) {
                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, new ArrayList<>());
            }
            HeadInfoDto headInfoDto = getHeadInfoDto();
            Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
            return this.returnSuccessJson(ExceptionEnums.SUCCESS,
                    userFeedbackBuService.getUserFeedbackList2(userId, Integer.parseInt(pageNo), Integer.parseInt(pageSize), locale));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

//    /**
//     * 添加用户反馈
//     * @return
//     */
//    @RequestMapping(value = "/saveuserfeedback")
//    public ReturnDto saveUserFeedback() {
//        try {
//            String userId = this.getValue("userid");
//            String clientInfo = this.getValue("sadais-agent");
//            UserFeedback addUserFeedbackDto = this.getBean(UserFeedback.class);
//
//            if (StringUtils.isBlank(userId) || addUserFeedbackDto == null) {
//                return this.returnSuccessJson(ExceptionEnums.PARAM_ERROR, "");
//            }
//
////            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userFeedbackBuService.addUserFeedback(
////                    userId, addUserFeedbackDto.getType(), addUserFeedbackDto.getQtype(), addUserFeedbackDto.getContent(), addUserFeedbackDto.getImages(), addUserFeedbackDto.getErrormsg(), addUserFeedbackDto.getMobile(),addUserFeedbackDto.getPrinterType(), clientInfo));
////       return this.returnSuccessJson(ExceptionEnums.SUCCESS,userFeedbackBuService.saveFeedbackWithImages(addUserFeedbackDto, imageList, userId));
//        } catch (ServiceException e) {
//            return this.returnFailJson(e);
//        }
//    }

    /**
     * 获取用户反馈详情
     * @return
     */
    @RequestMapping(value = "/getDetail")
    public ReturnDto getDetail() {
        try {
            String id = this.getValue("id");
            String mid = this.getValue("mid");
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, userFeedbackBuService.getById(id, mid));
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取需要反馈的机器类型
     * @return
     */
    @RequestMapping(value = "/getprintertype")
    public ReturnDto getPrinterType(){
        HeadInfoDto headInfoDto = getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
        return this.returnSuccessJson(ExceptionEnums.SUCCESS, userFeedbackBuService.getPrinterType(locale));
    }

    /**
     * 获取需要反馈的问题类型
     * @return
     */
    @RequestMapping(value = "/getproblemtype")
    public ReturnDto getProblemType(){
        HeadInfoDto headInfoDto = getHeadInfoDto();
        Locale locale = WebTools.getLocaleByLanguage(headInfoDto.getLanguage());
        return this.returnSuccessJson(ExceptionEnums.SUCCESS, userFeedbackBuService.getProblemType(locale));
    }
    /**
     * 提交用户反馈
     */
    @RequestMapping(value = "/saveuserfeedback")
    public ReturnDto submitFeedback() {
        try {
            String userId = this.getValue("userId");
            String type = this.getValue("type");
            String qtype = this.getValue("qtype");
            String content = this.getValue("content");
            String mobile = this.getValue("mobile");
            String clientInfo = this.getValue("clientInfo");
            String printerType = this.getValue("printerType");

            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户ID不能为空");
            }
            if (ToolsKit.isEmpty(content)) {
                throw new ServiceException("反馈内容不能为空");
            }

            UserFeedback feedback = new UserFeedback();
            feedback.setUserId(userId);
            feedback.setType(type);
            feedback.setQtype(qtype);
            feedback.setContent(content);
            feedback.setMobile(mobile);
            feedback.setClientInfo(clientInfo);
            feedback.setPrinterType(printerType);

            // 处理图片列表
            String imagesParam = this.getValue("images");
            List<String> imageList = new ArrayList<>();
            if (ToolsKit.isNotEmpty(imagesParam)) {
                // 假设前端传递的是逗号分隔的图片URL
                String[] images = imagesParam.split(",");
                for (String image : images) {
                    if (ToolsKit.isNotEmpty(image.trim())) {
                        imageList.add(image.trim());
                    }
                }
            }

            UserFeedback result = userFeedbackBuService.saveFeedbackWithImages(feedback, imageList, userId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 获取用户反馈列表
     */
    @RequestMapping(value = "/getUserFeedbacks")
    public ReturnDto getUserFeedbacks() {
        try {
            String userId = this.getValue("userId");

            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户ID不能为空");
            }

            List<UserFeedback> result = userFeedbackBuService.getFeedbackByUserId(userId);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 根据ID获取反馈详情
     */
    @RequestMapping(value = "/getFeedbackDetail")
    public ReturnDto getFeedbackDetail() {
        try {
            String id = this.getValue("id");

            if (ToolsKit.isEmpty(id)) {
                throw new ServiceException("反馈ID不能为空");
            }

            UserFeedback result = userFeedbackBuService.getById(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, result);
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 删除反馈
     */
    @RequestMapping(value = "/deleteFeedback")
    public ReturnDto deleteFeedback() {
        try {
            String id = this.getValue("id");
            String userId = this.getValue("userId");

            if (ToolsKit.isEmpty(id)) {
                throw new ServiceException("反馈ID不能为空");
            }
            if (ToolsKit.isEmpty(userId)) {
                throw new ServiceException("用户ID不能为空");
            }

            // 验证权限：只能删除自己的反馈
            UserFeedback feedback = userFeedbackBuService.getById(id);
            if (feedback == null) {
                throw new ServiceException("反馈不存在");
            }
            if (!userId.equals(feedback.getUserId())) {
                throw new ServiceException("无权限删除此反馈");
            }

            userFeedbackBuService.deleteById(id);
            return this.returnSuccessJson(ExceptionEnums.SUCCESS, "删除成功");
        } catch (ServiceException e) {
            return this.returnFailJson(e);
        }
    }

    /**
     * 测试接口
     */
    @RequestMapping(value = "/test")
    public ReturnDto test() {
        try {
            return this.returnSuccessJson("反馈控制器测试成功");
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }
}
