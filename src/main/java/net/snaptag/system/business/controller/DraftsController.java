package net.snaptag.system.business.controller;

import net.snaptag.system.business.buservice.DraftsBuService;
import net.snaptag.system.business.utils.Constant;
import net.snaptag.system.sadais.web.core.BaseController;
import net.snaptag.system.sadais.web.dto.ReturnDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 草稿控制器
 * 已修正JSON字段映射问题
 */
@RestController
@RequestMapping(Constant.PRE_MAPPING_URL + "/drafts")
public class DraftsController extends BaseController {
    
    @Autowired
    private DraftsBuService draftsBuService;

    /**
     * 测试接口
     */
    @RequestMapping(value = "/test")
    public ReturnDto test() {
        try {
            return this.returnSuccessJson("草稿控制器测试成功");
        } catch (Exception e) {
            return this.returnFailJson(e);
        }
    }

    // TODO: 添加其他草稿相关的接口方法
    // 暂时只保留基本的测试接口，避免编译错误
}
