package net.snaptag.system.util;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Mapper生成工具类
 * 用于批量生成MyBatis-Plus的Mapper接口
 */
public class MapperGenerator {
    
    private static final String MAPPER_PACKAGE = "net.snaptag.system.business.mapper";
    private static final String ENTITY_PACKAGE = "net.snaptag.system.business.entity";
    private static final String MAPPER_DIR = "src/main/java/net/snaptag/system/business/mapper/";
    
    // 需要生成Mapper的实体类列表
    private static final List<String> ENTITY_NAMES = Arrays.asList(
        "DeviceConnectLog", "Drafts", "FunctionsSetting", "Goods", "MaterialResource",
        "MsgCenter", "MsgColumn", "PaperDisplay", "PrintDriverUpdateInfo", "PrintPaperInfo",
        "ResourceData", "ShortLink", "SystemHelpItem", "SystemMsg", "TemplateDrafts",
        "TemplateDraftsLanguageConfig", "UpdateInfo", "UserFeedback", "UserTemplateDraftsCollect", "WebPrint"
    );
    
    public static void main(String[] args) {
        for (String entityName : ENTITY_NAMES) {
            generateMapper(entityName);
        }
        System.out.println("Mapper生成完成！");
    }
    
    private static void generateMapper(String entityName) {
        String mapperName = entityName + "Mapper";
        String fileName = MAPPER_DIR + mapperName + ".java";
        
        StringBuilder content = new StringBuilder();
        content.append("package ").append(MAPPER_PACKAGE).append(";\n\n");
        content.append("import com.baomidou.mybatisplus.core.mapper.BaseMapper;\n");
        content.append("import com.baomidou.mybatisplus.core.metadata.IPage;\n");
        content.append("import com.baomidou.mybatisplus.extension.plugins.pagination.Page;\n");
        content.append("import ").append(ENTITY_PACKAGE).append(".").append(entityName).append(";\n");
        content.append("import org.apache.ibatis.annotations.Mapper;\n");
        content.append("import org.apache.ibatis.annotations.Param;\n");
        content.append("import org.apache.ibatis.annotations.Select;\n\n");
        content.append("import java.util.List;\n\n");
        content.append("/**\n");
        content.append(" * ").append(entityName).append(" Mapper接口\n");
        content.append(" */\n");
        content.append("@Mapper\n");
        content.append("public interface ").append(mapperName).append(" extends BaseMapper<").append(entityName).append("> {\n\n");
        
        // 生成基本的查询方法
        String tableName = "v1_" + camelToSnake(entityName);
        
        content.append("    /**\n");
        content.append("     * 分页查询").append(entityName).append("列表\n");
        content.append("     */\n");
        content.append("    @Select(\"SELECT * FROM ").append(tableName).append(" WHERE status = #{status} ORDER BY createtime DESC\")\n");
        content.append("    IPage<").append(entityName).append("> findPage(Page<").append(entityName).append("> page, @Param(\"status\") String status);\n\n");
        
        content.append("    /**\n");
        content.append("     * 查询所有").append(entityName).append("列表\n");
        content.append("     */\n");
        content.append("    @Select(\"SELECT * FROM ").append(tableName).append(" WHERE status = #{status} ORDER BY createtime DESC\")\n");
        content.append("    List<").append(entityName).append("> findAllList(@Param(\"status\") String status);\n");
        
        content.append("}\n");
        
        // 写入文件
        try {
            File file = new File(fileName);
            file.getParentFile().mkdirs();
            FileWriter writer = new FileWriter(file);
            writer.write(content.toString());
            writer.close();
            System.out.println("生成Mapper: " + fileName);
        } catch (IOException e) {
            System.err.println("生成Mapper失败: " + fileName + ", 错误: " + e.getMessage());
        }
    }
    
    /**
     * 驼峰转下划线
     */
    private static String camelToSnake(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
}
