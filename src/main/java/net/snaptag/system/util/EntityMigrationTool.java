package net.snaptag.system.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * 实体类迁移工具
 * 用于批量将MongoDB实体类迁移到MySQL实体类
 */
public class EntityMigrationTool {
    
    // Business模块实体类
    private static final List<String> BUSINESS_ENTITIES = Arrays.asList(
        "Banner", "Material", "Dict", "DeviceConnectLog", "Drafts", "FunctionsSetting", 
        "Goods", "MaterialResource", "MsgCenter", "MsgColumn", "PaperDisplay", 
        "PrintDriverUpdateInfo", "PrintPaperInfo", "ResourceData", "ShortLink", 
        "SystemHelpItem", "SystemMsg", "TemplateDrafts", "TemplateDraftsLanguageConfig", 
        "UpdateInfo", "UserFeedback", "UserTemplateDraftsCollect", "WebPrint"
    );
    
    // Account模块实体类
    private static final List<String> ACCOUNT_ENTITIES = Arrays.asList(
        "ResourceTable", "RoleResource", "RoleTable", "UserAccount", "UserAuthAccount",
        "UserInfo", "UserLoginHistory", "UserRole", "UserThirdPartyAuth", "UserTitleType"
    );
    
    private static final String BUSINESS_ENTITY_DIR = "src/main/java/net/snaptag/system/business/entity/";
    private static final String ACCOUNT_ENTITY_DIR = "src/main/java/net/snaptag/system/account/entity/";
    private static final String BUSINESS_MAPPER_DIR = "src/main/java/net/snaptag/system/business/mapper/";
    private static final String ACCOUNT_MAPPER_DIR = "src/main/java/net/snaptag/system/account/mapper/";
    
    public static void main(String[] args) {
        System.out.println("开始实体类迁移...");
        
        // 创建mapper目录
        new File(BUSINESS_MAPPER_DIR).mkdirs();
        new File(ACCOUNT_MAPPER_DIR).mkdirs();
        new File("src/main/java/net/snaptag/system/account/mapper/").mkdirs();
        
        // 迁移Business实体类
        for (String entityName : BUSINESS_ENTITIES) {
            migrateBusinessEntity(entityName);
            generateMapper(entityName, "business");
        }
        
        // 迁移Account实体类
        for (String entityName : ACCOUNT_ENTITIES) {
            migrateAccountEntity(entityName);
            generateMapper(entityName, "account");
        }
        
        System.out.println("实体类迁移完成！");
    }
    
    private static void migrateBusinessEntity(String entityName) {
        String fileName = BUSINESS_ENTITY_DIR + entityName + ".java";
        migrateEntity(fileName, entityName, "business");
    }
    
    private static void migrateAccountEntity(String entityName) {
        String fileName = ACCOUNT_ENTITY_DIR + entityName + ".java";
        migrateEntity(fileName, entityName, "account");
    }
    
    private static void migrateEntity(String fileName, String entityName, String module) {
        try {
            Path path = Paths.get(fileName);
            if (!Files.exists(path)) {
                System.out.println("文件不存在: " + fileName);
                return;
            }
            
            String content = new String(Files.readAllBytes(path));
            
            // 替换导入
            content = content.replaceAll("import net\\.snaptag\\.system\\.sadais\\.mongo\\.common\\.IdEntity;", 
                "import net.snaptag.system.common.BaseEntity;");
            content = content.replaceAll("import org\\.springframework\\.data\\.mongodb\\.core\\.mapping\\.Document;", 
                "import com.baomidou.mybatisplus.annotation.TableName;");
            content = content.replaceAll("import org\\.springframework\\.data\\.mongodb\\.core\\.index\\.Indexed;", 
                "import com.baomidou.mybatisplus.annotation.TableField;");
            
            // 添加新的导入
            if (!content.contains("import lombok.Data;")) {
                content = content.replaceFirst("package [^;]+;", 
                    "$0\n\nimport com.baomidou.mybatisplus.annotation.TableField;\n" +
                    "import com.baomidou.mybatisplus.annotation.TableName;\n" +
                    "import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;\n" +
                    "import lombok.Data;\n" +
                    "import lombok.EqualsAndHashCode;\n" +
                    "import net.snaptag.system.common.BaseEntity;");
            }
            
            // 替换类注解和继承
            content = content.replaceAll("@Document\\(collection = \"[^\"]+\"\\)", 
                "@Data\n@EqualsAndHashCode(callSuper = true)\n@TableName(\"" + 
                "v1_" + camelToSnake(entityName) + "\")");
            content = content.replaceAll("extends BaseEntity", "extends BaseEntity");
            
            // 移除getter/setter方法（因为使用了Lombok）
            content = removeGettersSetters(content);
            
            Files.write(path, content.getBytes());
            System.out.println("迁移实体类: " + fileName);
            
        } catch (IOException e) {
            System.err.println("迁移实体类失败: " + fileName + ", 错误: " + e.getMessage());
        }
    }
    
    private static String removeGettersSetters(String content) {
        // 简单的getter/setter移除逻辑
        // 这里可以根据需要完善
        return content.replaceAll("\\s+public [^{]+\\{[^}]*return [^;]+;\\s*\\}", "")
                     .replaceAll("\\s+public void set[^{]+\\{[^}]*\\}", "");
    }
    
    private static void generateMapper(String entityName, String module) {
        String packageName = "net.snaptag.system." + module + ".mapper";
        String entityPackage = "net.snaptag.system." + module + ".entity";
        String mapperDir = module.equals("business") ? BUSINESS_MAPPER_DIR : ACCOUNT_MAPPER_DIR;
        
        String mapperName = entityName + "Mapper";
        String fileName = mapperDir + mapperName + ".java";
        
        StringBuilder content = new StringBuilder();
        content.append("package ").append(packageName).append(";\n\n");
        content.append("import com.baomidou.mybatisplus.core.mapper.BaseMapper;\n");
        content.append("import com.baomidou.mybatisplus.core.metadata.IPage;\n");
        content.append("import com.baomidou.mybatisplus.extension.plugins.pagination.Page;\n");
        content.append("import ").append(entityPackage).append(".").append(entityName).append(";\n");
        content.append("import org.apache.ibatis.annotations.Mapper;\n");
        content.append("import org.apache.ibatis.annotations.Param;\n");
        content.append("import org.apache.ibatis.annotations.Select;\n\n");
        content.append("import java.util.List;\n\n");
        content.append("/**\n");
        content.append(" * ").append(entityName).append(" Mapper接口\n");
        content.append(" */\n");
        content.append("@Mapper\n");
        content.append("public interface ").append(mapperName).append(" extends BaseMapper<").append(entityName).append("> {\n\n");
        
        String tableName = "v1_" + camelToSnake(entityName);
        
        content.append("    /**\n");
        content.append("     * 分页查询").append(entityName).append("列表\n");
        content.append("     */\n");
        content.append("    @Select(\"SELECT * FROM ").append(tableName).append(" WHERE status = #{status} ORDER BY createtime DESC\")\n");
        content.append("    IPage<").append(entityName).append("> findPage(Page<").append(entityName).append("> page, @Param(\"status\") String status);\n\n");
        
        content.append("    /**\n");
        content.append("     * 查询所有").append(entityName).append("列表\n");
        content.append("     */\n");
        content.append("    @Select(\"SELECT * FROM ").append(tableName).append(" WHERE status = #{status} ORDER BY createtime DESC\")\n");
        content.append("    List<").append(entityName).append("> findAllList(@Param(\"status\") String status);\n");
        
        content.append("}\n");
        
        try {
            Files.write(Paths.get(fileName), content.toString().getBytes());
            System.out.println("生成Mapper: " + fileName);
        } catch (IOException e) {
            System.err.println("生成Mapper失败: " + fileName + ", 错误: " + e.getMessage());
        }
    }
    
    private static String camelToSnake(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
}
